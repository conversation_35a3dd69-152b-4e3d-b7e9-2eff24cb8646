# 10KW太阳能逆变器电网电压配置修改指南

## ? 项目概述

**项目名称**: 10KW太阳能逆变器电网电压适配  
**目标**: 将电网电压从默认220V调整为190V进行并网运行  
**分析日期**: 2025年1月31日  
**技术平台**: TMS320F28335 DSP + CCS Studio 11.0.0  

---

## ? 当前系统配置分析

### 电网电压相关参数现状
- **额定电压**: 220V RMS
- **电网电压保护范围**: 183V - 246V (220V × 0.83 ~ 220V × 1.12)
- **同步检测阈值**: 132V (220V × 0.6)
- **母线电压保护**: 276V - 830V
- **电网频率保护**: 49.5Hz - 50.5Hz
- **保护延时**: 100ms (电网电压), 180ms (频率)

### 系统架构特点
- 双级保护机制: 50μs快速保护 + 1ms延时保护
- 17项保护参数全面覆盖
- SPLL_3ph_DDSRF三相锁相环同步
- SVPWM空间矢量PWM控制
- 自动相序检测和切换

---

## ? 修改方案详解

### 核心修改位置 (共5处)

#### 1. 系统额定电压定义
**文件**: `User/solar.c`  
**行号**: 第23行  
**当前代码**:
```c
#define RATED_VOLTAGE (220.0f)  // 额定电压 220V RMS
```
**修改为**:
```c
#define RATED_VOLTAGE (190.0f)  // 额定电压 190V RMS
```
**影响**: 系统基础电压参考值，影响所有电压相关计算

#### 2. 电网电压ADC采样校准系数
**文件**: `User/solar.c`  
**行号**: 第137-141行  
**当前代码**:
```c
Sample_Membe_Init(&h.Vgrid_a, RATED_VOLTAGE * 1.414f, ((0.2 * 220.0f / 212.0f)), 0);
Sample_Membe_Init(&h.Vgrid_b, RATED_VOLTAGE * 1.414f, ((0.2 * 220.0f / 212.0f)), 0);
Sample_Membe_Init(&h.Vgrid_c, RATED_VOLTAGE * 1.414f, ((0.2 * 220.0f / 213.0f)), 0);
```
**修改为**:
```c
Sample_Membe_Init(&h.Vgrid_a, RATED_VOLTAGE * 1.414f, ((0.2 * 190.0f / 212.0f)), 0);
Sample_Membe_Init(&h.Vgrid_b, RATED_VOLTAGE * 1.414f, ((0.2 * 190.0f / 212.0f)), 0);
Sample_Membe_Init(&h.Vgrid_c, RATED_VOLTAGE * 1.414f, ((0.2 * 190.0f / 213.0f)), 0);
```
**影响**: 确保电网电压ADC采样和转换的准确性

#### 3. 电网电压保护参数 (自动调整)
**文件**: `User/solar.c`  
**行号**: 第194-199行  
**说明**: 这些参数通过RATED_VOLTAGE自动计算，无需手动修改
- 过压保护: 190V × 1.12 = 212.8V ≈ 213V
- 欠压保护: 190V × 0.83 = 157.7V ≈ 158V
- 新保护范围: 158V - 213V (190V在安全范围内)

#### 4. 幅值同步检测阈值
**文件**: `User/solar.c`  
**行号**: 第551行  
**当前代码**:
```c
if (hPV->Vgrid_avg > (220.0f * 0.6f) && hPV->Vinv_avg > h.Vbus * (0.2f) && h.Vbus < 810.0f)
```
**修改为**:
```c
if (hPV->Vgrid_avg > (190.0f * 0.6f) && hPV->Vinv_avg > h.Vbus * (0.2f) && h.Vbus < 810.0f)
```
**影响**: 同步检测阈值从132V调整为114V，确保190V电网下正常同步

#### 5. 锁相环电压标准化系数 (自动调整)
**文件**: `User/solar.c`  
**行号**: 第1113行  
**说明**: 通过RATED_VOLTAGE自动计算，无需手动修改
```c
float GridMeasK = (1.0f / (RATED_VOLTAGE * 1.414f)); // 电网电压标准化系数
```

---

## ?? 安全性评估

### 电压保护范围验证
| 参数类型 | 当前值 | 修改后 | 变化幅度 | 安全评估 |
|---------|--------|--------|----------|----------|
| 额定电压 | 220V | 190V | -13.6% | ? 在合理范围 |
| 过压保护 | 246V | 213V | -13.4% | ? 保护有效 |
| 欠压保护 | 183V | 158V | -13.7% | ? 保护有效 |
| 同步阈值 | 132V | 114V | -13.6% | ? 同步正常 |
| 母线电压 | 276V-830V | 不变 | 0% | ? 无影响 |

### 保护机制确认
- ? 双级保护机制保持不变
- ? 17项保护参数中电压相关的自动调整
- ? 安全关闭序列保持不变
- ? 190V在新的保护范围(158V-213V)内

---

## ? 实施步骤

### 第一阶段: 核心参数修改
1. 修改 `RATED_VOLTAGE` 宏定义 (User/solar.c:23)
2. 修改电网电压采样校准系数 (User/solar.c:137-141)
3. 修改同步检测阈值 (User/solar.c:551)

### 第二阶段: 验证测试
1. 编译项目，检查无语法错误
2. 仿真测试电压保护功能
3. 验证锁相环同步性能
4. 检查SVPWM输出波形质量

### 第三阶段: 实际测试
1. 在190V电网环境下测试
2. 监控保护系统响应
3. 验证并网同步过程
4. 记录系统运行参数

---

## ? 技术参数对比

### 修改前后参数对比表
```
┌─────────────────┬─────────┬─────────┬──────────┐
│     参数名称     │  修改前  │  修改后  │   状态   │
├─────────────────┼─────────┼─────────┼──────────┤
│   额定电压(V)    │   220   │   190   │ 需要修改  │
│  过压保护(V)     │   246   │   213   │ 自动调整  │
│  欠压保护(V)     │   183   │   158   │ 自动调整  │
│  同步阈值(V)     │   132   │   114   │ 需要修改  │
│ 母线电压范围(V)  │ 276-830 │ 276-830 │  无变化  │
│  频率保护(Hz)    │49.5-50.5│49.5-50.5│  无变化  │
└─────────────────┴─────────┴─────────┴──────────┘
```

---

## ? 注意事项

### 修改前准备
- [ ] 备份原始代码
- [ ] 确认硬件支持190V电网
- [ ] 准备测试设备和环境
- [ ] 制定回滚方案

### 修改过程注意
- [ ] 严格按照修改清单执行
- [ ] 每次修改后立即编译验证
- [ ] 保持代码注释的准确性
- [ ] 记录所有修改内容

### 测试验证要求
- [ ] 功能测试: 验证基本并网功能
- [ ] 保护测试: 验证电压保护动作
- [ ] 性能测试: 验证效率和波形质量
- [ ] 长期测试: 验证系统稳定性

---

## ? 技术支持

如在实施过程中遇到问题，请参考以下资源：
- 项目技术文档: `User/` 目录下的头文件
- 保护系统说明: `User/protect.h`
- SVPWM控制说明: `User/dq_svpwm.h`
- DSP芯片手册: TMS320F28335技术参考手册

---

**文档版本**: v1.0  
**最后更新**: 2025年1月31日  
**状态**: 待实施
